import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import '../models/job_model.dart';

class JobService {
  final String baseUrl;
  final Dio _dio;
  final Logger _logger;

  JobService({
    required this.baseUrl,
    Dio? dio,
    Logger? logger,
  })  : _dio = dio ?? Dio(),
        _logger = logger ?? Logger();

  Future<List<Job>> fetchJobs() async {
    try {
      final response = await _dio.get('$baseUrl/jobs');
      final List<dynamic> jobListJson = response.data;
      return jobListJson.map((json) => Job.fromJson(json)).toList();
    } on DioException catch (e) {
      _logger.e('Failed to load jobs: ${e.message}');
      throw Exception('Failed to load jobs');
    }
  }

  Future<void> addJob(Job job) async {
    try {
      final response = await _dio.post(
        '$baseUrl/jobs',
        data: job.toJson(),
      );
      if (response.statusCode != 201) {
        throw Exception('Failed to add job: ${response.statusCode}');
      }
    } on DioException catch (e) {
      _logger.e('Failed to add job: ${e.message}');
      throw Exception('Failed to add job');
    }
  }

  Future<void> updateJob(Job job) async {
    try {
      final response = await _dio.put(
        '$baseUrl/jobs/${job.id}',
        data: job.toJson(),
      );
      if (response.statusCode != 200) {
        throw Exception('Failed to update job: ${response.statusCode}');
      }
    } on DioException catch (e) {
      _logger.e('Failed to update job: ${e.message}');
      throw Exception('Failed to update job');
    }
  }

  Future<void> deleteJob(String jobId) async {
    try {
      final response = await _dio.delete('$baseUrl/jobs/$jobId');
      if (response.statusCode != 200) {
        throw Exception('Failed to delete job: ${response.statusCode}');
      }
    } on DioException catch (e) {
      _logger.e('Failed to delete job: ${e.message}');
      throw Exception('Failed to delete job');
    }
  }
}
