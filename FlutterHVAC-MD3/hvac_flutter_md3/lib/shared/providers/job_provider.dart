import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

import '../models/job_model.dart';
import '../services/job_service.dart';
import 'dio_provider.dart';
import 'logger_provider.dart';

// TODO: Move base URL to a configuration file or environment variables
const String _jobApiBaseUrl = 'https://api.example.com';

final jobListProvider = StateNotifierProvider<JobListNotifier, AsyncValue<List<Job>>>((ref) {
  final dio = ref.watch(dioProvider);
  final logger = ref.watch(loggerProvider);
  final jobService = JobService(baseUrl: _jobApiBaseUrl, dio: dio, logger: logger);
  return JobListNotifier(jobService, logger);
});

class JobListNotifier extends StateNotifier<AsyncValue<List<Job>>> {
  final JobService jobService;
  final Logger _logger;

  JobListNotifier(this.jobService, this._logger) : super(const AsyncValue.loading()) {
    _fetchJobs();
  }

  Future<void> _fetchJobs() async {
    state = const AsyncValue.loading();
    try {
      final jobs = await jobService.fetchJobs();
      state = AsyncValue.data(jobs);
    } catch (e, st) {
      _logger.e('Error fetching jobs', error: e, stackTrace: st);
      state = AsyncValue.error(e, st);
    }
  }

  Future<void> addJob(Job job) async {
    state = const AsyncValue.loading();
    try {
      await jobService.addJob(job);
      await _fetchJobs(); // Re-fetch to ensure state is consistent with backend
    } catch (e, st) {
      _logger.e('Error adding job', error: e, stackTrace: st);
      state = AsyncValue.error(e, st);
    }
  }

  Future<void> updateJob(Job updatedJob) async {
    state = const AsyncValue.loading();
    try {
      await jobService.updateJob(updatedJob);
      await _fetchJobs(); // Re-fetch to ensure state is consistent with backend
    } catch (e, st) {
      _logger.e('Error updating job', error: e, stackTrace: st);
      state = AsyncValue.error(e, st);
    }
  }

  Future<void> deleteJob(String jobId) async {
    state = const AsyncValue.loading();
    try {
      await jobService.deleteJob(jobId);
      await _fetchJobs(); // Re-fetch to ensure state is consistent with backend
    } catch (e, st) {
      _logger.e('Error deleting job', error: e, stackTrace: st);
      state = AsyncValue.error(e, st);
    }
  }
}
