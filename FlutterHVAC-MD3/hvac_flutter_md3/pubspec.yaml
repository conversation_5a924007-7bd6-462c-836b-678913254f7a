name: hvac_flutter_md3
description: "HVAC CRM Flutter Application with Material Design 3"
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.7.0 <4.0.0'
  flutter: ">=3.24.0"

dependencies:
  flutter:
    sdk: flutter
  
  # Material Design 3 & UI
  material_color_utilities: ^0.11.1
  dynamic_color: ^1.7.0
  cupertino_icons: ^1.0.6
  json_annotation: ^4.8.1 # Added to resolve warning
  
  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  
  # Navigation
  go_router: ^15.1.2
  
  # Network & API
  dio: ^5.4.0
  grpc: ^4.0.4
  protobuf: ^4.1.0
  http: ^1.1.0
  
  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  flutter_secure_storage: ^10.0.0-beta.4
  shared_preferences: ^2.2.2
  
  # Charts & Visualization
  fl_chart: ^1.0.0
  
  # Calendar & Scheduling
  table_calendar: ^3.0.9
  
  # Camera & Media
  image_picker: ^1.0.4
  camera: ^0.11.1
  cached_network_image: ^3.3.0
  
  # Device Integration
  permission_handler: ^12.0.0+1
  connectivity_plus: ^6.1.4
  device_info_plus: ^11.4.0
  
  # Voice & Audio
  speech_to_text: ^7.0.0
  flutter_tts: ^4.2.2
  
  # Utilities
  intl: ^0.20.2
  uuid: ^4.2.1
  path_provider: ^2.1.1
  url_launcher: ^6.2.2
  
  # Animations
  lottie: ^3.3.1
  flutter_animate: ^4.2.0
  
  # Logging
  logger: ^2.0.2
  
  # Forms
  reactive_forms: ^18.0.0
  
  # UI Enhancements
  flutter_slidable: ^4.0.0
  shimmer: ^3.0.0

  # Mapping & Location
  google_maps_flutter: ^2.6.1 # Remember to set up API keys for Android and iOS

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  
  # Code Generation
  build_runner: ^2.4.7
  riverpod_generator: ^2.3.9
  hive_generator: ^2.0.1
  json_serializable: ^6.7.1
  json_annotation: ^4.8.1
  
  # Testing
  mockito: ^5.4.2
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/logos/
  
  fonts:
    - family: Roboto
      fonts:
        - asset: fonts/Roboto-Regular.ttf
        - asset: fonts/Roboto-Medium.ttf
          weight: 500
        - asset: fonts/Roboto-Bold.ttf
          weight: 700